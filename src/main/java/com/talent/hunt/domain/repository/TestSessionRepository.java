package com.talent.hunt.domain.repository;

import com.talent.hunt.domain.model.SessionStatus;
import com.talent.hunt.domain.model.Test;
import com.talent.hunt.domain.model.TestSession;
import com.talent.hunt.domain.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TestSessionRepository extends JpaRepository<TestSession, Long> {

    List<TestSession> findByUserOrderByCreatedAtDesc(User user);

    List<TestSession> findByTestOrderByCreatedAtDesc(Test test);

    Optional<TestSession> findByUserAndTestAndSessionStatus(User user, Test test, SessionStatus sessionStatus);

    @Query("SELECT ts FROM TestSession ts WHERE ts.user = :user AND ts.sessionStatus = 'IN_PROGRESS'")
    Optional<TestSession> findActiveSessionByUser(@Param("user") User user);

    @Query("SELECT ts FROM TestSession ts JOIN FETCH ts.userResponses WHERE ts.id = :sessionId")
    Optional<TestSession> findByIdWithUserResponses(@Param("sessionId") Long sessionId);

    @Query("SELECT COUNT(ts) FROM TestSession ts WHERE ts.test = :test AND ts.sessionStatus = 'COMPLETED'")
    long countCompletedSessionsByTest(@Param("test") Test test);

    @Query("SELECT COUNT(ts) FROM TestSession ts WHERE ts.user = :user AND ts.sessionStatus = 'COMPLETED'")
    long countCompletedSessionsByUser(@Param("user") User user);

    @Query("SELECT AVG(ts.score) FROM TestSession ts WHERE ts.test = :test AND ts.sessionStatus = 'COMPLETED'")
    Optional<Double> findAverageScoreByTest(@Param("test") Test test);
}
