package com.talent.hunt.application.service;

import com.talent.hunt.domain.model.*;
import com.talent.hunt.domain.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class TestSessionService {

    private final TestSessionRepository testSessionRepository;
    private final TestRepository testRepository;
    private final QuestionRepository questionRepository;
    private final UserResponseRepository userResponseRepository;
    private final AnswerOptionRepository answerOptionRepository;

    public TestSession startTestSession(Long testId, User user) {
        log.info("Starting test session for test ID: {} by user: {}", testId, user.getUsername());
        
        Test test = testRepository.findById(testId)
                .orElseThrow(() -> new IllegalArgumentException("Test not found: " + testId));
        
        if (!test.canBeStartedBy(user)) {
            throw new IllegalArgumentException("Test cannot be started");
        }
        
        // Check if user already has an active session for this test
        Optional<TestSession> existingSession = testSessionRepository
                .findByUserAndTestAndSessionStatus(user, test, SessionStatus.IN_PROGRESS);
        
        if (existingSession.isPresent()) {
            throw new IllegalArgumentException("User already has an active session for this test");
        }
        
        TestSession testSession = TestSession.builder()
                .test(test)
                .user(user)
                .sessionStatus(SessionStatus.NOT_STARTED)
                .build();
        
        TestSession savedSession = testSessionRepository.save(testSession);
        savedSession.startSession();
        testSessionRepository.save(savedSession);
        
        log.info("Test session started successfully: {}", savedSession.getId());
        return savedSession;
    }

    public UserResponse submitAnswer(Long sessionId, Long questionId, Long selectedOptionId, User user) {
        log.info("Submitting answer for session: {}, question: {}, option: {}", 
                sessionId, questionId, selectedOptionId);
        
        TestSession session = testSessionRepository.findById(sessionId)
                .orElseThrow(() -> new IllegalArgumentException("Test session not found: " + sessionId));
        
        if (!session.getUser().getId().equals(user.getId())) {
            throw new IllegalArgumentException("User not authorized for this session");
        }
        
        if (session.getSessionStatus() != SessionStatus.IN_PROGRESS) {
            throw new IllegalArgumentException("Session is not in progress");
        }
        
        if (session.isExpired()) {
            session.abandonSession();
            testSessionRepository.save(session);
            throw new IllegalArgumentException("Session has expired");
        }
        
        Question question = questionRepository.findById(questionId)
                .orElseThrow(() -> new IllegalArgumentException("Question not found: " + questionId));
        
        AnswerOption selectedOption = answerOptionRepository.findById(selectedOptionId)
                .orElseThrow(() -> new IllegalArgumentException("Answer option not found: " + selectedOptionId));
        
        // Check if answer already exists for this question in this session
        Optional<UserResponse> existingResponse = userResponseRepository
                .findByTestSessionAndQuestion(session, question);
        
        UserResponse userResponse;
        if (existingResponse.isPresent()) {
            userResponse = existingResponse.get();
            userResponse.submitAnswer(selectedOption);
        } else {
            userResponse = UserResponse.builder()
                    .testSession(session)
                    .question(question)
                    .build();
            userResponse.submitAnswer(selectedOption);
        }
        
        UserResponse savedResponse = userResponseRepository.save(userResponse);
        log.info("Answer submitted successfully for session: {}", sessionId);
        return savedResponse;
    }

    public TestSession completeTestSession(Long sessionId, User user) {
        log.info("Completing test session: {} by user: {}", sessionId, user.getUsername());
        
        TestSession session = testSessionRepository.findById(sessionId)
                .orElseThrow(() -> new IllegalArgumentException("Test session not found: " + sessionId));
        
        if (!session.getUser().getId().equals(user.getId())) {
            throw new IllegalArgumentException("User not authorized for this session");
        }
        
        if (session.getSessionStatus() != SessionStatus.IN_PROGRESS) {
            throw new IllegalArgumentException("Session is not in progress");
        }
        
        session.completeSession();
        TestSession completedSession = testSessionRepository.save(session);
        
        log.info("Test session completed successfully: {}", sessionId);
        return completedSession;
    }

    @Transactional(readOnly = true)
    public Optional<TestSession> findActiveSessionByUser(User user) {
        return testSessionRepository.findActiveSessionByUser(user);
    }

    @Transactional(readOnly = true)
    public Optional<TestSession> findSessionById(Long sessionId) {
        return testSessionRepository.findById(sessionId);
    }

    @Transactional(readOnly = true)
    public Optional<TestSession> findSessionWithResponses(Long sessionId) {
        return testSessionRepository.findByIdWithUserResponses(sessionId);
    }

    @Transactional(readOnly = true)
    public List<TestSession> findUserSessions(User user) {
        return testSessionRepository.findByUserOrderByCreatedAtDesc(user);
    }

    @Transactional(readOnly = true)
    public List<TestSession> findTestSessions(Test test) {
        return testSessionRepository.findByTestOrderByCreatedAtDesc(test);
    }

    @Transactional(readOnly = true)
    public List<UserResponse> getSessionResponses(Long sessionId) {
        TestSession session = testSessionRepository.findById(sessionId)
                .orElseThrow(() -> new IllegalArgumentException("Test session not found: " + sessionId));
        return userResponseRepository.findByTestSessionOrderByAnsweredAtAsc(session);
    }

    public void checkAndExpireSessions() {
        // This method can be called periodically to expire sessions that have exceeded their time limit
        List<TestSession> inProgressSessions = testSessionRepository
                .findAll()
                .stream()
                .filter(session -> session.getSessionStatus() == SessionStatus.IN_PROGRESS)
                .filter(TestSession::isExpired)
                .toList();
        
        for (TestSession session : inProgressSessions) {
            session.abandonSession();
            testSessionRepository.save(session);
            log.info("Expired session: {}", session.getId());
        }
    }
}
